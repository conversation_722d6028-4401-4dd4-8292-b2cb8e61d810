import uuid
import logging
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

from langchain_ollama import <PERSON><PERSON><PERSON>lla<PERSON>
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import Run<PERSON>blePassthrough
from langchain_core.output_parsers import StrOutputParser

from models.website_chatbot import WebsiteChatbot, ChatbotConfig, ChatbotStatus, CreateChatbotRequest
from models.conversation import Conversation, Message, MessageRole, IntentType, CreateConversationRequest, SendMessageRequest
from services.vectorstore_service import VectorStoreService
from services.website_crawler_service import WebsiteCrawlerService
from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class ChatbotService:
    """Service for managing website chatbots and conversations"""
    
    def __init__(self):
        """Initialize the chatbot service"""
        self.vectorstore_service = VectorStoreService()
        self.website_crawler_service = WebsiteCrawlerService()
        self._chatbots: Dict[str, WebsiteChatbot] = {}
        self._llm_cache: Dict[str, ChatOllama] = {}
        
        # Ensure directories exist
        import os
        os.makedirs("data/chatbots", exist_ok=True)
        os.makedirs("data/conversations", exist_ok=True)
    
    def create_website_chatbot(self, request: CreateChatbotRequest) -> WebsiteChatbot:
        """
        Create a new website chatbot
        
        Args:
            request: Chatbot creation request
            
        Returns:
            Created chatbot instance
        """
        try:
            # Generate unique chatbot ID
            chatbot_id = f"chatbot_{uuid.uuid4().hex[:12]}"
            
            # Create chatbot instance
            chatbot = WebsiteChatbot(
                id=chatbot_id,
                website_url=request.website_url,
                company_name=request.company_name,
                config=request.config,
                status=ChatbotStatus.CREATING,
                vectorstore_path=f"qdrant_db/websites/{chatbot_id}"
            )
            
            # Store chatbot
            self._chatbots[chatbot_id] = chatbot
            
            # Generate embed code
            chatbot.embed_code = self._generate_embed_code(chatbot_id, request.website_url)
            
            # Save chatbot to storage
            self._save_chatbot(chatbot)
            
            logger.info(f"Created website chatbot: {chatbot_id} for {request.website_url}")
            return chatbot
            
        except Exception as e:
            logger.error(f"Error creating chatbot: {e}")
            raise RuntimeError(f"Failed to create chatbot: {e}")
    
    def get_chatbot(self, chatbot_id: str) -> Optional[WebsiteChatbot]:
        """Get chatbot by ID"""
        if chatbot_id in self._chatbots:
            return self._chatbots[chatbot_id]
        
        # Try to load from storage
        chatbot = self._load_chatbot(chatbot_id)
        if chatbot:
            self._chatbots[chatbot_id] = chatbot
        
        return chatbot
    
    def get_all_chatbots(self) -> List[WebsiteChatbot]:
        """Get all chatbots"""
        # Load all chatbots from storage
        import os
        chatbots = []
        chatbots_dir = "data/chatbots"
        
        if os.path.exists(chatbots_dir):
            for filename in os.listdir(chatbots_dir):
                if filename.endswith('.json'):
                    chatbot_id = filename[:-5]  # Remove .json extension
                    chatbot = self.get_chatbot(chatbot_id)
                    if chatbot:
                        chatbots.append(chatbot)
        
        return chatbots
    
    def update_chatbot(self, chatbot_id: str, updates: Dict[str, Any]) -> Optional[WebsiteChatbot]:
        """Update chatbot configuration"""
        chatbot = self.get_chatbot(chatbot_id)
        if not chatbot:
            return None
        
        try:
            # Update fields
            for key, value in updates.items():
                if hasattr(chatbot, key):
                    setattr(chatbot, key, value)
            
            chatbot.updated_at = datetime.utcnow()
            
            # Save updated chatbot
            self._save_chatbot(chatbot)
            
            logger.info(f"Updated chatbot: {chatbot_id}")
            return chatbot
            
        except Exception as e:
            logger.error(f"Error updating chatbot {chatbot_id}: {e}")
            return None
    
    def delete_chatbot(self, chatbot_id: str) -> bool:
        """Delete a chatbot and its associated data"""
        try:
            # Remove from memory
            if chatbot_id in self._chatbots:
                del self._chatbots[chatbot_id]
            
            # Delete vector store
            self.vectorstore_service.delete_website_vectorstore(chatbot_id)
            
            # Delete chatbot file
            import os
            chatbot_file = f"data/chatbots/{chatbot_id}.json"
            if os.path.exists(chatbot_file):
                os.remove(chatbot_file)
            
            logger.info(f"Deleted chatbot: {chatbot_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting chatbot {chatbot_id}: {e}")
            return False
    
    async def train_chatbot(self, chatbot_id: str) -> Dict[str, Any]:
        """
        Train a chatbot by crawling its website and indexing content
        
        Args:
            chatbot_id: Chatbot ID to train
            
        Returns:
            Training results
        """
        chatbot = self.get_chatbot(chatbot_id)
        if not chatbot:
            return {'success': False, 'message': 'Chatbot not found'}
        
        try:
            # Update status to training
            chatbot.update_status(ChatbotStatus.TRAINING)
            self._save_chatbot(chatbot)
            
            # Crawl and index website
            crawl_results = await self.website_crawler_service.crawl_and_index_website(
                chatbot.website_url, 
                chatbot_id
            )
            
            if crawl_results['success']:
                # Update chatbot with training results
                chatbot.knowledge_base_size = crawl_results['documents_count']
                chatbot.last_training = datetime.utcnow()
                chatbot.update_status(ChatbotStatus.ACTIVE)
                
                # Save updated chatbot
                self._save_chatbot(chatbot)
                
                logger.info(f"Chatbot {chatbot_id} training completed successfully")
                return {
                    'success': True,
                    'message': f'Training completed. Indexed {crawl_results["documents_count"]} documents.',
                    'documents_count': crawl_results['documents_count']
                }
            else:
                chatbot.update_status(ChatbotStatus.ERROR)
                self._save_chatbot(chatbot)
                
                return crawl_results
                
        except Exception as e:
            logger.error(f"Error training chatbot {chatbot_id}: {e}")
            chatbot.update_status(ChatbotStatus.ERROR)
            self._save_chatbot(chatbot)
            
            return {
                'success': False,
                'message': f'Training failed: {str(e)}'
            }
    
    def create_conversation(self, request: CreateConversationRequest) -> Conversation:
        """Create a new conversation"""
        try:
            conversation_id = f"conv_{uuid.uuid4().hex[:12]}"
            
            conversation = Conversation(
                id=conversation_id,
                chatbot_id=request.chatbot_id,
                session_id=request.session_id,
                user_id=request.user_id
            )
            
            # Save conversation
            self._save_conversation(conversation)
            
            logger.info(f"Created conversation: {conversation_id} for chatbot: {request.chatbot_id}")
            return conversation
            
        except Exception as e:
            logger.error(f"Error creating conversation: {e}")
            raise RuntimeError(f"Failed to create conversation: {e}")
    
    async def process_message(self, request: SendMessageRequest) -> Dict[str, Any]:
        """
        Process a user message and generate a response
        
        Args:
            request: Message request
            
        Returns:
            Response with AI-generated answer
        """
        start_time = time.time()
        
        try:
            # Get conversation
            conversation = self._load_conversation(request.conversation_id)
            if not conversation:
                return {'success': False, 'message': 'Conversation not found'}
            
            # Get chatbot
            chatbot = self.get_chatbot(conversation.chatbot_id)
            if not chatbot:
                return {'success': False, 'message': 'Chatbot not found'}
            
            # Create user message
            user_message = Message(
                id=f"msg_{uuid.uuid4().hex[:12]}",
                conversation_id=request.conversation_id,
                content=request.content,
                role=MessageRole.USER,
                session_id=request.session_id
            )
            
            # Detect intent
            intent_result = await self._detect_intent(request.content)
            user_message.detected_intent = intent_result['intent']
            user_message.confidence_score = intent_result['confidence']
            
            # Check for lead qualification
            if chatbot.config.lead_qualification_enabled:
                lead_result = await self._qualify_lead(request.content, intent_result)
                user_message.is_lead_qualified = lead_result['is_lead']
                user_message.lead_score = lead_result['score']
            
            # Add message to conversation
            conversation.add_message(user_message)
            
            # Generate AI response
            ai_response = await self._generate_response(
                request.content, 
                conversation.chatbot_id, 
                chatbot.config
            )
            
            # Create assistant message
            assistant_message = Message(
                id=f"msg_{uuid.uuid4().hex[:12]}",
                conversation_id=request.conversation_id,
                content=ai_response,
                role=MessageRole.ASSISTANT,
                session_id=request.session_id
            )
            
            # Add assistant message
            conversation.add_message(assistant_message)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            user_message.processing_time = processing_time
            
            # Update conversation metrics
            conversation.lead_qualified = user_message.is_lead_qualified or False
            conversation.lead_score = user_message.lead_score or 0.0
            
            # Save conversation
            self._save_conversation(conversation)
            
            # Update chatbot metrics
            chatbot.increment_metrics(conversations=0, messages=2)
            self._save_chatbot(chatbot)
            
            return {
                'success': True,
                'response': ai_response,
                'intent': user_message.detected_intent,
                'confidence': user_message.confidence_score,
                'lead_qualified': user_message.is_lead_qualified,
                'lead_score': user_message.lead_score,
                'processing_time': processing_time
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                'success': False,
                'message': f'Error processing message: {str(e)}'
            }
    
    async def _detect_intent(self, message: str) -> Dict[str, Any]:
        """Detect user intent from message"""
        try:
            # Simple keyword-based intent detection
            message_lower = message.lower()
            
            intent_keywords = {
                IntentType.PRODUCT_INQUIRY: ['product', 'service', 'feature', 'capability', 'what can', 'how does'],
                IntentType.PRICING_INQUIRY: ['price', 'cost', 'pricing', 'plan', 'subscription', 'fee', 'how much'],
                IntentType.SUPPORT_REQUEST: ['help', 'support', 'issue', 'problem', 'error', 'trouble', 'broken'],
                IntentType.APPOINTMENT_BOOKING: ['appointment', 'schedule', 'book', 'meeting', 'consultation', 'demo'],
                IntentType.LEAD_QUALIFICATION: ['interested', 'learn more', 'contact', 'sales', 'representative', 'quote']
            }
            
            best_intent = IntentType.UNKNOWN
            best_score = 0.0
            
            for intent, keywords in intent_keywords.items():
                score = sum(1 for keyword in keywords if keyword in message_lower)
                if score > best_score:
                    best_score = score
                    best_intent = intent
            
            confidence = min(0.9, 0.3 + (best_score * 0.2))
            
            return {
                'intent': best_intent,
                'confidence': confidence,
                'score': best_score
            }
            
        except Exception as e:
            logger.warning(f"Error in intent detection: {e}")
            return {
                'intent': IntentType.UNKNOWN,
                'confidence': 0.0,
                'score': 0
            }
    
    async def _qualify_lead(self, message: str, intent_result: Dict[str, Any]) -> Dict[str, Any]:
        """Qualify if message represents a sales lead"""
        try:
            message_lower = message.lower()
            
            # Lead qualification keywords
            lead_keywords = [
                'interested', 'learn more', 'contact', 'sales', 'representative',
                'quote', 'pricing', 'demo', 'trial', 'purchase', 'buy', 'cost',
                'compare', 'evaluate', 'solution', 'need', 'looking for'
            ]
            
            # Calculate lead score
            keyword_matches = sum(1 for keyword in lead_keywords if keyword in message_lower)
            base_score = min(1.0, keyword_matches * 0.2)
            
            # Boost score for high-confidence intents
            if intent_result['intent'] in [IntentType.PRICING_INQUIRY, IntentType.LEAD_QUALIFICATION]:
                base_score += 0.3
            
            # Determine if it's a qualified lead
            is_lead = base_score >= 0.4
            
            return {
                'is_lead': is_lead,
                'score': min(1.0, base_score)
            }
            
        except Exception as e:
            logger.warning(f"Error in lead qualification: {e}")
            return {'is_lead': False, 'score': 0.0}
    
    async def _generate_response(self, message: str, chatbot_id: str, config: ChatbotConfig) -> str:
        """Generate AI response using RAG"""
        try:
            # Get relevant documents
            documents = self.vectorstore_service.search_website_documents(message, chatbot_id, k=3)
            
            if documents:
                # Get the full chatbot object to access company_name
                chatbot = self.get_chatbot(chatbot_id)
                company_name = chatbot.company_name if chatbot else "the company"
                
                # Use RAG with website content
                template = f"""You are {config.name}, an AI assistant for {company_name}.

{config.personality.value.capitalize()} personality: {config.greeting_message}

Based on the following context from the website, provide a helpful and accurate response:

Context:
{{context}}

User Question: {{question}}

Please provide a response that is:
- Helpful and informative
- Based on the website content
- In a {config.personality.value} tone
- Under {config.max_response_length} characters

Response:"""
                
                prompt = ChatPromptTemplate.from_template(template)
                llm = self._get_llm(chatbot_id, config)
                
                rag_chain = (
                    {"context": lambda x: "\n\n".join([doc.page_content for doc in documents]), "question": RunnablePassthrough()} |
                    prompt |
                    llm |
                    StrOutputParser()
                )
                
                response = rag_chain.invoke(message)
                return response[:config.max_response_length]
            else:
                # Fallback response
                return config.fallback_message
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return config.fallback_message
    
    def _get_llm(self, chatbot_id: str, config: ChatbotConfig) -> ChatOllama:
        """Get or create LLM instance for chatbot"""
        if chatbot_id in self._llm_cache:
            return self._llm_cache[chatbot_id]
        
        llm = ChatOllama(
            model=settings.LLM_MODEL,
            temperature=config.temperature,
            num_ctx=config.max_context_length
        )
        
        self._llm_cache[chatbot_id] = llm
        return llm
    
    def _generate_embed_code(self, chatbot_id: str, website_url: str) -> str:
        """Generate HTML/JavaScript code for website embedding"""
        embed_code = f"""<!-- Website Chatbot for {website_url} -->
<div id="chatbot-widget-{chatbot_id}" class="chatbot-widget"></div>
<script>
(function() {{
    var script = document.createElement('script');
    script.src = 'https://yourdomain.com/widget.js?id={chatbot_id}';
    script.async = true;
    document.head.appendChild(script);
}})();
</script>"""
        
        return embed_code
    
    def _save_chatbot(self, chatbot: WebsiteChatbot):
        """Save chatbot to storage"""
        import json
        import os
        
        try:
            chatbot_file = f"data/chatbots/{chatbot.id}.json"
            os.makedirs(os.path.dirname(chatbot_file), exist_ok=True)
            
            with open(chatbot_file, 'w') as f:
                json.dump(chatbot.dict(), f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving chatbot {chatbot.id}: {e}")
    
    def _load_chatbot(self, chatbot_id: str) -> Optional[WebsiteChatbot]:
        """Load chatbot from storage"""
        import json
        import os
        
        try:
            chatbot_file = f"data/chatbots/{chatbot_id}.json"
            if os.path.exists(chatbot_file):
                with open(chatbot_file, 'r') as f:
                    data = json.load(f)
                    return WebsiteChatbot(**data)
        except Exception as e:
            logger.error(f"Error loading chatbot {chatbot_id}: {e}")
        
        return None
    
    def _save_conversation(self, conversation: Conversation):
        """Save conversation to storage"""
        import json
        import os
        
        try:
            conversation_file = f"data/conversations/{conversation.id}.json"
            os.makedirs(os.path.dirname(conversation_file), exist_ok=True)
            
            with open(conversation_file, 'w') as f:
                json.dump(conversation.dict(), f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving conversation {conversation.id}: {e}")
    
    def _load_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Load conversation from storage"""
        import json
        import os
        
        try:
            conversation_file = f"data/conversations/{conversation_id}.json"
            if os.path.exists(conversation_file):
                with open(conversation_file, 'r') as f:
                    data = json.load(f)
                    return Conversation(**data)
        except Exception as e:
            logger.error(f"Error loading conversation {conversation_id}: {e}")
        
        return None 