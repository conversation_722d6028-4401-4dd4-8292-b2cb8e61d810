<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Chatbot Service - Test Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        .test-btn:hover {
            background: #5a6fd8;
        }

        .response-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .status-bar {
            background: rgba(128, 65, 65, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            color: white;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
        }

        .status-offline {
            background: #dc3545;
        }

        .status-connecting {
            background: #ffc107;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Website Chatbot Service</h1>
            <p>Complete Test Dashboard - Test Every Feature</p>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-indicator status-connecting"></span>
                    <strong>Backend Service</strong><br>
                    <span id="backend-status">Checking...</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-connecting"></span>
                    <strong>Authentication</strong><br>
                    <span id="auth-status">Checking...</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-connecting"></span>
                    <strong>Database</strong><br>
                    <span id="db-status">Checking...</span>
                </div>
            </div>
        </div>

        <!-- Core Service Testing -->
        <div class="test-section">
            <h2>🔧 Core Service Testing</h2>
            <p>Test fundamental service endpoints:</p>

            <div class="test-buttons">
                <button class="test-btn" onclick="testEndpoint('GET', '/', 'Service Info')">Service Info</button>
                <button class="test-btn" onclick="testEndpoint('GET', '/health', 'Health Check')">Health Check</button>
                <button class="test-btn" onclick="testEndpoint('GET', '/docs', 'API Docs')">API Docs</button>
                <button class="test-btn" onclick="testEndpoint('GET', '/widget.js', 'Widget JS')">Widget JS</button>
            </div>

            <div class="response-area" id="core-response">
                Click buttons above to test core endpoints...
            </div>
        </div>

        <!-- Authentication Testing -->
        <div class="test-section">
            <h2>🔐 Authentication Testing</h2>
            <p>Test authentication endpoints:</p>

            <div class="test-buttons">
                <button class="test-btn"
                    onclick="testAuthEndpoint('GET', '/chatbot/analytics/overview', 'Analytics')">Test Auth
                    Required</button>
                <button class="test-btn" onclick="testAuthEndpoint('GET', '/chatbot', 'List Chatbots')">List
                    Chatbots</button>
                <button class="test-btn" onclick="testInvalidAuth()">Test Invalid Auth</button>
            </div>

            <div class="response-area" id="auth-response">
                Click buttons above to test authentication...
            </div>
        </div>

        <!-- Chatbot Management -->
        <div class="test-section">
            <h2>🤖 Chatbot Management</h2>
            <p>Create and manage chatbots:</p>

            <div class="form-group">
                <label>Website URL:</label>
                <input type="url" id="website-url" value="http://localhost:8080/test_dashboard.html">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>Company Name:</label>
                    <input type="text" id="company-name" value="Test Company">
                </div>
                <div class="form-group">
                    <label>Chatbot Name:</label>
                    <input type="text" id="chatbot-name" value="Test Assistant">
                </div>
            </div>

            <div class="form-group">
                <label>Greeting Message:</label>
                <textarea id="greeting-message"
                    rows="2">Hello! I'm your test assistant. How can I help you today?</textarea>
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="createChatbot()">Create Chatbot</button>
                <button class="test-btn" onclick="listChatbots()">List Chatbots</button>
                <button class="test-btn" onclick="getChatbotDetails()">Get Details</button>
                <button class="test-btn" onclick="updateChatbot()">Update Chatbot</button>
                <button class="test-btn" onclick="deleteChatbot()">Delete Chatbot</button>
            </div>

            <div class="response-area" id="chatbot-response">
                Use form above to manage chatbots...
            </div>
        </div>

        <!-- Training & Processing -->
        <div class="test-section">
            <h2>🔄 Training & Processing</h2>
            <p>Test chatbot training:</p>

            <div class="test-buttons">
                <button class="test-btn" onclick="trainChatbot()">Train Chatbot</button>
                <button class="test-btn" onclick="checkTrainingStatus()">Check Status</button>
            </div>

            <div class="response-area" id="training-response">
                Test training functionality...
            </div>
        </div>

        <!-- Conversations -->
        <div class="test-section">
            <h2>💬 Conversations</h2>
            <p>Test conversation management:</p>

            <div class="form-row">
                <div class="form-group">
                    <label>Chatbot ID:</label>
                    <input type="text" id="conversation-chatbot-id" placeholder="Enter chatbot ID">
                </div>
                <div class="form-group">
                    <label>Session ID:</label>
                    <input type="text" id="session-id" value="test_session_123">
                </div>
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="createConversation()">Create Conversation</button>
                <button class="test-btn" onclick="listConversations()">List Conversations</button>
            </div>

            <div class="response-area" id="conversation-response">
                Create conversations to test...
            </div>
        </div>

        <!-- Message Testing -->
        <div class="test-section">
            <h2>📝 Message Testing</h2>
            <p>Test AI message responses:</p>

            <div class="form-group">
                <label>Conversation ID:</label>
                <input type="text" id="message-conversation-id" placeholder="Enter conversation ID">
            </div>

            <div class="form-group">
                <label>Message:</label>
                <textarea id="message-content" rows="3">Hello! Can you tell me about your services?</textarea>
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="sendMessage()">Send Message</button>
                <button class="test-btn" onclick="testMultipleMessages()">Test Multiple</button>
            </div>

            <div class="response-area" id="message-response">
                Send messages to test AI responses...
            </div>
        </div>

        <!-- Analytics -->
        <div class="test-section">
            <h2>📊 Analytics</h2>
            <p>Test analytics endpoints:</p>

            <div class="test-buttons">
                <button class="test-btn" onclick="getOverviewAnalytics()">Overview Analytics</button>
                <button class="test-btn" onclick="getChatbotAnalytics()">Chatbot Analytics</button>
            </div>

            <div class="response-area" id="analytics-response">
                Test analytics functionality...
            </div>
        </div>

        <!-- Widget Testing -->
        <div class="test-section">
            <h2>🌐 Widget Testing</h2>
            <p>Test the chatbot widget:</p>

            <div class="form-group">
                <label>Chatbot ID for Widget:</label>
                <input type="text" id="widget-chatbot-id" placeholder="Enter chatbot ID">
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="loadWidget()">Load Widget</button>
                <button class="test-btn" onclick="testWidgetAPI()">Test Widget API</button>
            </div>

            <div class="response-area" id="widget-response">
                Load widget to test integration...
            </div>
        </div>

        <div style="text-align: center; color: white; margin-top: 30px;">
            <p>&copy; 2024 Website Chatbot Service - Test Dashboard</p>
        </div>
    </div>

    <!-- Chatbot Widget -->
    <div id="chatbot-widget-demo" class="chatbot-widget"></div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:8000';
        const API_TOKEN = 'changeme';
        let currentChatbotId = null;
        let currentConversationId = null;

        // Status Checking
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    document.getElementById('backend-status').textContent = 'Online';
                    document.querySelector('#backend-status').parentElement.querySelector('.status-indicator').className = 'status-indicator status-online';
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = 'Offline';
                document.querySelector('#backend-status').parentElement.querySelector('.status-indicator').className = 'status-indicator status-offline';
            }
        }

        // Core Service Testing
        async function testEndpoint(method, endpoint, description) {
            const responseArea = document.getElementById('core-response');
            responseArea.textContent = `Testing ${method} ${endpoint}...`;

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, { method });
                const data = await response.text();
                responseArea.textContent = `${description} (${response.status}):\n${data}`;
            } catch (error) {
                responseArea.textContent = `Error testing ${endpoint}: ${error.message}`;
            }
        }

        // Authentication Testing
        async function testAuthEndpoint(method, endpoint, description) {
            const responseArea = document.getElementById('auth-response');
            responseArea.textContent = `Testing ${method} ${endpoint} with authentication...`;

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method,
                    headers: { 'Authorization': `Bearer ${API_TOKEN}` }
                });
                const data = await response.json();
                responseArea.textContent = `${description} (${response.status}):\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error testing ${endpoint}: ${error.message}`;
            }
        }

        async function testInvalidAuth() {
            const responseArea = document.getElementById('auth-response');
            responseArea.textContent = 'Testing invalid authentication...';

            try {
                const response = await fetch(`${API_BASE}/chatbot/analytics/overview`, {
                    headers: { 'Authorization': 'Bearer invalid-token' }
                });
                const data = await response.json();
                responseArea.textContent = `Invalid auth test (${response.status}):\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error testing invalid auth: ${error.message}`;
            }
        }

        // Chatbot Management
        async function createChatbot() {
            const responseArea = document.getElementById('chatbot-response');
            responseArea.textContent = 'Creating chatbot...';

            const chatbotData = {
                website_url: document.getElementById('website-url').value,
                company_name: document.getElementById('company-name').value,
                config: {
                    name: document.getElementById('chatbot-name').value,
                    description: 'Test chatbot created via dashboard',
                    personality: 'friendly',
                    greeting_message: document.getElementById('greeting-message').value
                }
            };

            try {
                const response = await fetch(`${API_BASE}/chatbot/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_TOKEN}`
                    },
                    body: JSON.stringify(chatbotData)
                });

                const data = await response.json();

                if (data.success) {
                    currentChatbotId = data.data.chatbot_id;
                    document.getElementById('conversation-chatbot-id').value = currentChatbotId;
                    document.getElementById('widget-chatbot-id').value = currentChatbotId;
                    responseArea.textContent = `Chatbot created successfully!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    responseArea.textContent = `Failed to create chatbot:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                responseArea.textContent = `Error creating chatbot: ${error.message}`;
            }
        }

        async function listChatbots() {
            const responseArea = document.getElementById('chatbot-response');
            responseArea.textContent = 'Listing chatbots...';

            try {
                const response = await fetch(`${API_BASE}/chatbot`, {
                    headers: { 'Authorization': `Bearer ${API_TOKEN}` }
                });

                const data = await response.json();
                responseArea.textContent = `Chatbots retrieved:\n${JSON.stringify(data, null, 2)}`;

                if (data.success && data.data.chatbots && data.data.chatbots.length > 0) {
                    const firstChatbot = data.data.chatbots[0];
                    currentChatbotId = firstChatbot.id;
                    document.getElementById('conversation-chatbot-id').value = currentChatbotId;
                    document.getElementById('widget-chatbot-id').value = currentChatbotId;
                }
            } catch (error) {
                responseArea.textContent = `Error listing chatbots: ${error.message}`;
            }
        }

        async function getChatbotDetails() {
            if (!currentChatbotId) {
                document.getElementById('chatbot-response').textContent = 'Please create or select a chatbot first.';
                return;
            }

            const responseArea = document.getElementById('chatbot-response');
            responseArea.textContent = 'Getting chatbot details...';

            try {
                const response = await fetch(`${API_BASE}/chatbot/${currentChatbotId}`, {
                    headers: { 'Authorization': `Bearer ${API_TOKEN}` }
                });

                const data = await response.json();
                responseArea.textContent = `Chatbot details:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error getting chatbot details: ${error.message}`;
            }
        }

        async function updateChatbot() {
            if (!currentChatbotId) {
                document.getElementById('chatbot-response').textContent = 'Please create or select a chatbot first.';
                return;
            }

            const responseArea = document.getElementById('chatbot-response');
            responseArea.textContent = 'Updating chatbot...';

            const updateData = {
                config: {
                    name: document.getElementById('chatbot-name').value + ' (Updated)',
                    greeting_message: document.getElementById('greeting-message').value + ' (Updated)'
                }
            };

            try {
                const response = await fetch(`${API_BASE}/chatbot/${currentChatbotId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_TOKEN}`
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();
                responseArea.textContent = `Chatbot updated:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error updating chatbot: ${error.message}`;
            }
        }

        async function deleteChatbot() {
            if (!currentChatbotId) {
                document.getElementById('chatbot-response').textContent = 'Please create or select a chatbot first.';
                return;
            }

            if (!confirm('Are you sure you want to delete this chatbot?')) {
                return;
            }

            const responseArea = document.getElementById('chatbot-response');
            responseArea.textContent = 'Deleting chatbot...';

            try {
                const response = await fetch(`${API_BASE}/chatbot/${currentChatbotId}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${API_TOKEN}` }
                });

                const data = await response.json();
                responseArea.textContent = `Chatbot deleted:\n${JSON.stringify(data, null, 2)}`;

                currentChatbotId = null;
                document.getElementById('conversation-chatbot-id').value = '';
                document.getElementById('widget-chatbot-id').value = '';
            } catch (error) {
                responseArea.textContent = `Error deleting chatbot: ${error.message}`;
            }
        }

        // Training
        async function trainChatbot() {
            if (!currentChatbotId) {
                document.getElementById('training-response').textContent = 'Please create or select a chatbot first.';
                return;
            }

            const responseArea = document.getElementById('training-response');
            responseArea.textContent = 'Starting chatbot training...';

            try {
                const response = await fetch(`${API_BASE}/chatbot/${currentChatbotId}/train`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${API_TOKEN}` }
                });

                const data = await response.json();
                responseArea.textContent = `Training started:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error starting training: ${error.message}`;
            }
        }

        // Conversations
        async function createConversation() {
            if (!currentChatbotId) {
                document.getElementById('conversation-response').textContent = 'Please create or select a chatbot first.';
                return;
            }

            const responseArea = document.getElementById('conversation-response');
            responseArea.textContent = 'Creating conversation...';

            const conversationData = {
                chatbot_id: currentChatbotId,
                session_id: document.getElementById('session-id').value
            };

            try {
                const response = await fetch(`${API_BASE}/chatbot/conversation`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(conversationData)
                });

                const data = await response.json();

                if (data.success) {
                    currentConversationId = data.id;
                    document.getElementById('message-conversation-id').value = currentConversationId;
                    responseArea.textContent = `Conversation created:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    responseArea.textContent = `Failed to create conversation:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                responseArea.textContent = `Error creating conversation: ${error.message}`;
            }
        }

        // Messages
        async function sendMessage() {
            if (!currentConversationId) {
                document.getElementById('message-response').textContent = 'Please create a conversation first.';
                return;
            }

            const responseArea = document.getElementById('message-response');
            const message = document.getElementById('message-content').value;

            if (!message.trim()) {
                responseArea.textContent = 'Please enter a message.';
                return;
            }

            responseArea.textContent = 'Sending message...';

            const messageData = {
                conversation_id: currentConversationId,
                content: message,
                session_id: document.getElementById('session-id').value
            };

            try {
                const response = await fetch(`${API_BASE}/chatbot/message`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(messageData)
                });

                const data = await response.json();
                responseArea.textContent = `Message sent:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error sending message: ${error.message}`;
            }
        }

        // Analytics
        async function getOverviewAnalytics() {
            const responseArea = document.getElementById('analytics-response');
            responseArea.textContent = 'Getting overview analytics...';

            try {
                const response = await fetch(`${API_BASE}/chatbot/analytics/overview`, {
                    headers: { 'Authorization': `Bearer ${API_TOKEN}` }
                });

                const data = await response.json();
                responseArea.textContent = `Overview analytics:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseArea.textContent = `Error getting analytics: ${error.message}`;
            }
        }

        // Widget
        async function loadWidget() {
            if (!currentChatbotId) {
                document.getElementById('widget-response').textContent = 'Please create or select a chatbot first.';
                return;
            }

            const responseArea = document.getElementById('widget-response');
            responseArea.textContent = 'Loading widget...';

            try {
                const script = document.createElement('script');
                script.src = `${API_BASE}/widget.js?id=${currentChatbotId}`;
                script.async = true;
                document.head.appendChild(script);

                responseArea.textContent = 'Widget script loaded successfully! Check the bottom-right corner for the chatbot widget.';
            } catch (error) {
                responseArea.textContent = `Error loading widget: ${error.message}`;
            }
        }

        // Initialize dashboard
        window.addEventListener('load', function () {
            checkServiceStatus();
            setInterval(checkServiceStatus, 30000);
            document.getElementById('session-id').value = 'session_' + Date.now();
        });

        // Placeholder functions
        function checkTrainingStatus() { document.getElementById('training-response').textContent = 'Training status checking not yet implemented.'; }
        function listConversations() { document.getElementById('conversation-response').textContent = 'Conversation listing not yet implemented.'; }
        function testMultipleMessages() { document.getElementById('message-response').textContent = 'Multiple message testing not yet implemented.'; }
        function getChatbotAnalytics() { document.getElementById('analytics-response').textContent = 'Chatbot analytics not yet implemented.'; }
        function testWidgetAPI() { document.getElementById('widget-response').textContent = 'Widget API testing not yet implemented.'; }
    </script>
</body>

</html>