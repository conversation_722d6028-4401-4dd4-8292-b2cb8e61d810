# 🌐 Website Chatbot Service

A production-ready, scalable AI-powered website chatbot service built with FastAPI and LangChain. Create intelligent chatbots for any website in minutes with automatic content crawling, RAG-powered responses, and beautiful embeddable widgets.

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-green.svg)](https://fastapi.tiangolo.com)
[![Lang<PERSON>hain](https://img.shields.io/badge/LangChain-0.1+-purple.svg)](https://langchain.com)
[![Ollama](https://img.shields.io/badge/Ollama-Local%20LLM-orange.svg)](https://ollama.ai)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> **🚀 Transform any website into an intelligent conversational experience with local AI models!**

## ✨ Features

### 🤖 **AI-Powered Chatbots**

- **RAG (Retrieval-Augmented Generation)** for accurate, context-aware responses
- **Multi-modal processing** - PDFs, images, and website content
- **Intent detection** and smart query routing
- **Lead qualification** and business intelligence
- **Customizable personalities** and brand voice

### 🌐 **Website Integration**

- **Automatic website crawling** with intelligent content extraction
- **SEO-friendly content processing** (titles, meta descriptions, main content)
- **Robots.txt and sitemap support** for respectful crawling
- **Real-time content updates** and synchronization
- **Cross-origin widget embedding** for any website

### 🎨 **Beautiful Widgets**

- **Modern, responsive design** with glass morphism effects
- **Customizable appearance** (colors, logos, branding)
- **Mobile-optimized** interface
- **Real-time typing indicators** and animations
- **Unread message notifications**

### 📊 **Analytics & Insights**

- **Conversation tracking** and user behavior analysis
- **Lead generation metrics** and conversion tracking
- **Response time monitoring** and performance analytics
- **Business intelligence** dashboards
- **Export and reporting** capabilities

### 🔒 **Enterprise Features**

- **Multi-tenant architecture** for multiple websites
- **Role-based access control** and permissions
- **API token authentication** and security
- **Rate limiting** and request throttling
- **Audit logging** and compliance

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Website       │    │   Chatbot        │    │   Vector        │
│   Widget        │◄──►│   Service        │◄──►│   Database      │
│   (JavaScript)  │    │   (FastAPI)      │    │   (Qdrant)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Website        │
                       │   Crawler        │
                       │   Service        │
                       └──────────────────┘
```

## 📁 Project Structure

```
nodev-agent-provider/
├── 📁 models/              # Data models and schemas
│   ├── conversation.py     # Conversation and message models
│   └── website_chatbot.py  # Chatbot configuration models
├── 📁 services/            # Business logic services
│   ├── auth_service.py     # Authentication and authorization
│   ├── chatbot_service.py  # Core chatbot functionality
│   ├── vectorstore_service.py  # Vector database operations
│   └── website_crawler_service.py  # Web crawling and content extraction
├── 📁 utils/               # Utility functions
│   ├── config.py          # Configuration management
│   └── logger.py          # Logging setup
├── 📁 static/              # Static files
│   └── widget.js          # Embeddable chat widget
├── 📁 data/                # Data storage (gitignored)
├── 📁 qdrant_db/           # Vector database files (gitignored)
├── app.py                 # FastAPI application
├── start.py               # Application startup script
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🚀 Quick Start

### 1. **Prerequisites**

- Python 3.8+ installed
- Git installed
- 8GB+ RAM recommended for local LLM models

### 2. **Installation**

```bash
# Clone the repository
git clone https://github.com/yourusername/website-chatbot-service.git
cd website-chatbot-service

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install Ollama (Local LLM server)
curl -fsSL https://ollama.com/install.sh | sh  # Linux/Mac
# For Windows: Download from https://ollama.ai

# Pull required models
ollama pull gemma3:latest        # Main LLM model
ollama pull nomic-embed-text     # Embedding model
```

### 3. **Configuration**

Create a `.env` file in the project root:

```env
# Application Settings
DEBUG=false
SECRET_KEY=your-secret-key-change-this-in-production
API_TOKEN=your-api-token-change-this-in-production

# AI Models (using available models)
EMBED_MODEL=nomic-embed-text
LLM_MODEL=gemma3:latest

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_PATH=qdrant_db

# Security
MAX_REQUESTS_PER_MINUTE=100
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Website Crawling
MAX_CRAWL_DEPTH=3
MAX_PAGES_PER_WEBSITE=100
CRAWL_DELAY=1.0
```

> **⚠️ Important**: Change the `SECRET_KEY` and `API_TOKEN` values for production use!

### 4. **Start the Services**

**Terminal 1 - Start Ollama Server:**

```bash
ollama serve
```

**Terminal 2 - Start Chatbot Service:**

```bash
# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start the service
python start.py
```

**Alternative startup methods:**

```bash
# Using uvicorn directly (for development)
uvicorn app:app --host 0.0.0.0 --port 8000 --reload

# Using the startup script (recommended)
python start.py
```

### 5. **Verify Installation**

Once both services are running, you should see:

```
🚀 Starting Website Chatbot Service...
📖 API Documentation: http://localhost:8000/docs
🔧 Health Check: http://localhost:8000/health
🌐 Widget JavaScript: http://localhost:8000/widget.js

INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

**Test the service:**

- Health Check: http://localhost:8000/health
- API Documentation: http://localhost:8000/docs
- Interactive API: http://localhost:8000/docs#/

### 6. **Create Your First Chatbot**

```bash
# Create a chatbot for your website
curl -X POST "http://localhost:8000/chatbot/create" \
  -H "Authorization: Bearer your-api-token-change-this-in-production" \
  -H "Content-Type: application/json" \
  -d '{
    "website_url": "https://example.com",
    "company_name": "Example Corp",
    "config": {
      "name": "Example Assistant",
      "description": "AI assistant for Example Corp",
      "personality": "professional",
      "greeting_message": "Hello! How can I help you today?",
      "primary_color": "#667eea",
      "secondary_color": "#764ba2"
    }
  }'
```

**Expected Response:**

```json
{
  "success": true,
  "message": "Website chatbot created successfully",
  "data": {
    "chatbot_id": "chatbot_abc123def456",
    "embed_code": "<!-- Website Chatbot for https://example.com -->...",
    "status": "creating"
  }
}
```

### 7. **Embed on Your Website**

Copy the generated embed code to your website:

```html
<!-- Website Chatbot for https://example.com -->
<div id="chatbot-widget-chatbot_abc123" class="chatbot-widget"></div>
<script>
  (function () {
    var script = document.createElement("script");
    script.src = "http://localhost:8000/widget.js?id=chatbot_abc123";
    script.async = true;
    document.head.appendChild(script);
  })();
</script>
```

## 📚 API Reference

### **Chatbot Management**

| Endpoint              | Method | Description                      |
| --------------------- | ------ | -------------------------------- |
| `/chatbot/create`     | POST   | Create a new website chatbot     |
| `/chatbot/{id}`       | GET    | Get chatbot details              |
| `/chatbot`            | GET    | List all chatbots                |
| `/chatbot/{id}`       | PUT    | Update chatbot configuration     |
| `/chatbot/{id}`       | DELETE | Delete chatbot                   |
| `/chatbot/{id}/train` | POST   | Train chatbot on website content |

### **Conversation Management**

| Endpoint                | Method | Description             |
| ----------------------- | ------ | ----------------------- |
| `/chatbot/conversation` | POST   | Create new conversation |
| `/chatbot/message`      | POST   | Send message to chatbot |

### **Analytics**

| Endpoint                      | Method | Description            |
| ----------------------------- | ------ | ---------------------- |
| `/chatbot/{id}/analytics`     | GET    | Get chatbot analytics  |
| `/chatbot/analytics/overview` | GET    | Get overview analytics |

### **System**

| Endpoint     | Method | Description                   |
| ------------ | ------ | ----------------------------- |
| `/`          | GET    | Service health check          |
| `/health`    | GET    | Health status                 |
| `/widget.js` | GET    | Embeddable widget JavaScript  |
| `/docs`      | GET    | Interactive API documentation |

## 🎯 Use Cases

### **E-commerce**

- **Product recommendations** based on user queries
- **Order tracking** and customer support
- **Size and availability** information
- **Return and refund** assistance

### **Business Services**

- **Appointment scheduling** and booking
- **Service information** and pricing
- **Lead qualification** and sales support
- **FAQ and support** automation

### **Content Websites**

- **Article recommendations** and search
- **Content navigation** and discovery
- **User engagement** and interaction
- **Personalized experiences**

## 🔧 Configuration Options

### **Chatbot Personalities**

```python
from models.website_chatbot import ChatbotPersonality

# Available personalities
ChatbotPersonality.PROFESSIONAL    # Formal, business-like
ChatbotPersonality.FRIENDLY        # Warm, approachable
ChatbotPersonality.CASUAL          # Informal, relaxed
ChatbotPersonality.TECHNICAL       # Detailed, technical
ChatbotPersonality.SALES           # Persuasive, conversion-focused
```

### **Content Processing**

```python
# Configure what content to include
config = ChatbotConfig(
    include_website_content=True,      # Crawl website
    include_uploaded_documents=True,   # Process PDFs/images
    custom_knowledge="Additional info", # Custom knowledge base
    max_crawl_depth=3,                 # How deep to crawl
    max_pages_per_website=100          # Max pages to process
)
```

### **AI Response Settings**

```python
config = ChatbotConfig(
    temperature=0.1,              # Response creativity (0.0-1.0)
    max_context_length=8192,      # Context window size
    max_response_length=500,      # Max response length
    lead_qualification_enabled=True,  # Enable lead scoring
    human_handoff_enabled=True    # Enable human escalation
)
```

## 📊 Monitoring & Analytics

### **Key Metrics**

- **Conversation Volume**: Total conversations and messages
- **Response Quality**: User satisfaction and engagement
- **Lead Generation**: Qualified leads and conversion rates
- **Performance**: Response times and system health
- **Content Coverage**: Knowledge base size and freshness

### **Business Intelligence**

- **User Intent Analysis**: What users are asking about
- **Content Gaps**: Missing information users need
- **Peak Usage Times**: When users are most active
- **Geographic Distribution**: Where users are located
- **Device Preferences**: Mobile vs desktop usage

## 🚀 Scaling & Deployment

### **Production Deployment**

```bash
# Using Docker
docker build -t website-chatbot .
docker run -p 8000:8000 website-chatbot

# Using systemd service
sudo systemctl enable website-chatbot
sudo systemctl start website-chatbot

# Using Kubernetes
kubectl apply -f k8s/
```

### **Performance Optimization**

- **Vector store caching** for faster responses
- **Connection pooling** for database efficiency
- **Background processing** for non-blocking operations
- **Rate limiting** to prevent abuse
- **Load balancing** for high availability

### **Monitoring & Alerting**

- **Health checks** and uptime monitoring
- **Performance metrics** and alerting
- **Error tracking** and logging
- **Resource utilization** monitoring
- **Business metrics** dashboards

## 🔒 Security & Compliance

### **Data Protection**

- **Encryption at rest** for sensitive data
- **Secure API authentication** with tokens
- **Rate limiting** to prevent abuse
- **Input validation** and sanitization
- **Audit logging** for compliance

### **Privacy & GDPR**

- **User consent** management
- **Data retention** policies
- **Right to be forgotten** implementation
- **Data export** capabilities
- **Privacy policy** integration

## 🔧 Troubleshooting

### **Common Issues**

**1. Ollama Connection Error**

```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Restart Ollama service
ollama serve
```

**2. Model Not Found**

```bash
# List available models
ollama list

# Pull required models
ollama pull gemma3:latest
ollama pull nomic-embed-text
```

**3. Port Already in Use**

```bash
# Find process using port 8000
lsof -i :8000  # Linux/Mac
netstat -ano | findstr :8000  # Windows

# Kill the process or change port in .env
PORT=8001
```

**4. Vector Database Issues**

```bash
# Clear vector database
rm -rf qdrant_db/

# Restart the service
python start.py
```

### **FAQ**

**Q: Can I use different LLM models?**
A: Yes! Update the `LLM_MODEL` in your `.env` file. Supported models include `llama3.1`, `mistral`, `qwen2.5`, etc.

**Q: How do I customize the chat widget appearance?**
A: Modify the `primary_color` and `secondary_color` in your chatbot configuration, or edit `static/widget.js` directly.

**Q: Can I deploy this on cloud platforms?**
A: Absolutely! The service works on AWS, GCP, Azure, DigitalOcean, and other cloud platforms. See the deployment section for details.

**Q: Is this suitable for production use?**
A: Yes, but ensure you configure proper security settings, use a production database, and implement monitoring.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### **Development Setup**

```bash
# Install development dependencies
pip install -r requirements.txt

# Run tests
pytest

# Code formatting
black .
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.example.com](https://docs.example.com)
- **API Reference**: [api.example.com](https://api.example.com)
- **Community**: [community.example.com](https://community.example.com)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- **LangChain** for the AI/ML framework
- **FastAPI** for the web framework
- **Qdrant** for vector database
- **Ollama** for local LLM hosting
- **OpenAI** for inspiration and research

---

**Ready to transform your website with AI?** 🚀

Start building intelligent chatbots today with our comprehensive platform!
