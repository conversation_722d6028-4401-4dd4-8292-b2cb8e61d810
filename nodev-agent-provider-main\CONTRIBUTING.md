# Contributing to Website Chatbot Service

Thank you for your interest in contributing to the Website Chatbot Service! We welcome contributions from the community.

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- Git
- Ollama installed locally
- Basic knowledge of FastAPI, LangChain, and AI/ML concepts

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/yourusername/website-chatbot-service.git
   cd website-chatbot-service
   ```

2. **Set up development environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Install pre-commit hooks** (optional but recommended)
   ```bash
   pip install pre-commit
   pre-commit install
   ```

## 🛠️ Development Guidelines

### Code Style

- Follow PEP 8 Python style guidelines
- Use type hints where possible
- Write docstrings for all functions and classes
- Use meaningful variable and function names

### Code Formatting

We use `black` for code formatting:

```bash
# Format all Python files
black .

# Check formatting without making changes
black --check .
```

### Testing

- Write tests for new features
- Ensure all tests pass before submitting PR
- Aim for good test coverage

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=.
```

## 📝 Contribution Process

### 1. Create an Issue

Before starting work, create an issue describing:
- The problem you're solving
- Your proposed solution
- Any breaking changes

### 2. Create a Branch

```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/your-bug-fix
```

### 3. Make Changes

- Write clean, well-documented code
- Add tests for new functionality
- Update documentation as needed

### 4. Commit Changes

Use conventional commit messages:

```bash
git commit -m "feat: add new chatbot personality options"
git commit -m "fix: resolve vector store connection issue"
git commit -m "docs: update API documentation"
```

### 5. Submit Pull Request

- Push your branch to your fork
- Create a pull request with a clear description
- Link to any related issues
- Ensure CI checks pass

## 🎯 Areas for Contribution

### High Priority
- Performance optimizations
- Additional LLM model support
- Enhanced security features
- Better error handling
- Comprehensive testing

### Medium Priority
- UI/UX improvements
- Additional integrations
- Documentation improvements
- Example applications
- Deployment guides

### Low Priority
- Code refactoring
- Minor bug fixes
- Feature enhancements

## 📋 Pull Request Checklist

- [ ] Code follows project style guidelines
- [ ] Tests added for new functionality
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Commit messages follow conventional format
- [ ] No breaking changes (or clearly documented)
- [ ] PR description clearly explains changes

## 🐛 Bug Reports

When reporting bugs, please include:

- Python version
- Operating system
- Steps to reproduce
- Expected vs actual behavior
- Error messages/logs
- Minimal code example

## 💡 Feature Requests

For feature requests, please provide:

- Clear description of the feature
- Use case and motivation
- Proposed implementation approach
- Any breaking changes

## 📞 Getting Help

- Create an issue for questions
- Join our community discussions
- Check existing documentation

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for contributing to make this project better! 🚀
