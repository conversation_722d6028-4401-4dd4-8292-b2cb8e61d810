import os
import logging
from typing import Dict, Any

from fastapi import <PERSON>AP<PERSON>, BackgroundTasks, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# Import our modules
from models.website_chatbot import WebsiteChatbot, ChatbotConfig, CreateChatbotRequest, UpdateChatbotRequest, ChatbotResponse
from models.conversation import CreateConversationRequest, SendMessageRequest
from services.vectorstore_service import VectorStoreService
from services.website_crawler_service import WebsiteCrawlerService
from services.chatbot_service import ChatbotService
from services.auth_service import AuthService, get_current_user, require_permission
from utils.config import settings
from utils.logger import setup_logger

# Load configuration
logger = setup_logger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Website Chatbot Service",
    description="AI-powered website chatbots with RAG capabilities",
    version="1.0.0"
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize services
vectorstore_service = VectorStoreService()
website_crawler_service = WebsiteCrawlerService()
chatbot_service = ChatbotService()
auth_service = AuthService()

@app.on_event("startup")
async def startup_event():
    """Initialize resources on startup"""
    logger.info("Starting Website Chatbot Service...")
    try:
        # Ensure directories exist
        os.makedirs("data/chatbots", exist_ok=True)
        os.makedirs("data/conversations", exist_ok=True)
        os.makedirs(settings.QDRANT_PATH, exist_ok=True)
        
        logger.info("Service initialized successfully")
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise e

@app.get("/")
async def root():
    """Service health check"""
    return {
        "service": "Website Chatbot Service",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}

@app.get("/test-auth")
async def test_auth():
    """Test endpoint to debug authentication"""
    return {
        "message": "Auth test endpoint working",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.get("/test-auth-simple")
async def test_auth_simple(request: Request):
    """Simple auth test without HTTPBearer dependency"""
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return {"error": "No valid authorization header"}
    
    token = auth_header.split(" ")[1]
    try:
        user = auth_service.validate_api_token(token)
        return {"message": "Authentication successful", "user": user}
    except Exception as e:
        return {"error": f"Authentication failed: {str(e)}"}

# Website Chatbot API Endpoints
@app.post("/chatbot/create", response_model=ChatbotResponse)
async def create_website_chatbot(
    request: CreateChatbotRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new website chatbot"""
    try:
        # Create chatbot
        chatbot = chatbot_service.create_website_chatbot(request)
        
        # Start training in background
        background_tasks.add_task(chatbot_service.train_chatbot, chatbot.id)
        
        return ChatbotResponse(
            success=True,
            message="Website chatbot created successfully",
            data={
                "chatbot_id": chatbot.id,
                "embed_code": chatbot.embed_code,
                "status": chatbot.status.value
            }
        )
    except Exception as e:
        logger.error(f"Error creating chatbot: {e}")
        return ChatbotResponse(
            success=False,
            message="Failed to create chatbot",
            error=str(e)
        )

@app.get("/chatbot/{chatbot_id}", response_model=ChatbotResponse)
async def get_website_chatbot(
    chatbot_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get website chatbot by ID"""
    try:
        chatbot = chatbot_service.get_chatbot(chatbot_id)
        if not chatbot:
            return ChatbotResponse(
                success=False,
                message="Chatbot not found",
                error="Chatbot with specified ID does not exist"
            )
        
        return ChatbotResponse(
            success=True,
            message="Chatbot retrieved successfully",
            data=chatbot.dict()
        )
    except Exception as e:
        logger.error(f"Error retrieving chatbot {chatbot_id}: {e}")
        return ChatbotResponse(
            success=False,
            message="Failed to retrieve chatbot",
            error=str(e)
        )

@app.get("/chatbot", response_model=ChatbotResponse)
async def list_website_chatbots(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """List all website chatbots"""
    try:
        chatbots = chatbot_service.get_all_chatbots()
        return ChatbotResponse(
            success=True,
            message=f"Retrieved {len(chatbots)} chatbots",
            data={"chatbots": [chatbot.dict() for chatbot in chatbots]}
        )
    except Exception as e:
        logger.error(f"Error listing chatbots: {e}")
        return ChatbotResponse(
            success=False,
            message="Failed to list chatbots",
            error=str(e)
        )

@app.put("/chatbot/{chatbot_id}", response_model=ChatbotResponse)
async def update_website_chatbot(
    chatbot_id: str,
    request: UpdateChatbotRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update website chatbot configuration"""
    try:
        updates = {}
        if request.config:
            updates['config'] = request.config.dict()
        if request.status:
            updates['status'] = request.status
        
        chatbot = chatbot_service.update_chatbot(chatbot_id, updates)
        if not chatbot:
            return ChatbotResponse(
                success=False,
                message="Chatbot not found",
                error="Chatbot with specified ID does not exist"
            )
        
        return ChatbotResponse(
            success=True,
            message="Chatbot updated successfully",
            data=chatbot.dict()
        )
    except Exception as e:
        logger.error(f"Error updating chatbot {chatbot_id}: {e}")
        return ChatbotResponse(
            success=False,
            message="Failed to update chatbot",
            error=str(e)
        )

@app.delete("/chatbot/{chatbot_id}", response_model=ChatbotResponse)
async def delete_website_chatbot(
    chatbot_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Delete website chatbot"""
    try:
        success = chatbot_service.delete_chatbot(chatbot_id)
        if not success:
            return ChatbotResponse(
                success=False,
                message="Failed to delete chatbot",
                error="Chatbot deletion failed"
            )
        
        return ChatbotResponse(
            success=True,
            message="Chatbot deleted successfully"
        )
    except Exception as e:
        logger.error(f"Error deleting chatbot {chatbot_id}: {e}")
        return ChatbotResponse(
            success=False,
            message="Failed to delete chatbot",
            error=str(e)
        )

@app.post("/chatbot/{chatbot_id}/train", response_model=ChatbotResponse)
async def train_website_chatbot(
    chatbot_id: str,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Train website chatbot by crawling website"""
    try:
        # Start training in background
        background_tasks.add_task(chatbot_service.train_chatbot, chatbot_id)
        
        return ChatbotResponse(
            success=True,
            message="Chatbot training started",
            data={"chatbot_id": chatbot_id, "status": "training"}
        )
    except Exception as e:
        logger.error(f"Error starting chatbot training {chatbot_id}: {e}")
        return ChatbotResponse(
            success=False,
            message="Failed to start chatbot training",
            error=str(e)
        )

@app.post("/chatbot/conversation", response_model=Dict[str, Any])
async def create_conversation(
    request: CreateConversationRequest
):
    """Create a new conversation for a chatbot"""
    try:
        conversation = chatbot_service.create_conversation(request)
        return {
            "success": True,
            "id": conversation.id,
            "chatbot_id": conversation.chatbot_id,
            "session_id": conversation.session_id,
            "started_at": conversation.started_at
        }
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/chatbot/message", response_model=Dict[str, Any])
async def send_message(
    request: SendMessageRequest
):
    """Send a message to a chatbot conversation"""
    try:
        response = await chatbot_service.process_message(request)
        return response
    except Exception as e:
        logger.error(f"Error processing message: {e}")
        return {
            "success": False,
            "message": f"Error processing message: {str(e)}"
        }

@app.get("/chatbot/{chatbot_id}/analytics", response_model=Dict[str, Any])
async def get_chatbot_analytics(
    chatbot_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get analytics for a specific chatbot"""
    try:
        chatbot = chatbot_service.get_chatbot(chatbot_id)
        if not chatbot:
            return {"success": False, "error": "Chatbot not found"}
        
        # Get vector store info
        vectorstore_info = vectorstore_service.get_collection_info(chatbot_id)
        
        return {
            "success": True,
            "chatbot_id": chatbot_id,
            "analytics": {
                "total_conversations": chatbot.total_conversations,
                "total_messages": chatbot.total_messages,
                "average_response_time": chatbot.average_response_time,
                "knowledge_base_size": chatbot.knowledge_base_size,
                "last_training": chatbot.last_training,
                "status": chatbot.status.value
            },
            "vectorstore": vectorstore_info
        }
    except Exception as e:
        logger.error(f"Error getting analytics for chatbot {chatbot_id}: {e}")
        return {"success": False, "error": str(e)}

@app.get("/chatbot/analytics/overview", response_model=Dict[str, Any])
async def get_overview_analytics(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get overview analytics for all chatbots"""
    try:
        chatbots = chatbot_service.get_all_chatbots()
        vectorstores = vectorstore_service.get_all_vectorstores()
        
        total_conversations = sum(c.total_conversations for c in chatbots)
        total_messages = sum(c.total_messages for c in chatbots)
        active_chatbots = len([c for c in chatbots if c.status.value == 'active'])
        
        return {
            "success": True,
            "overview": {
                "total_chatbots": len(chatbots),
                "active_chatbots": active_chatbots,
                "total_conversations": total_conversations,
                "total_messages": total_messages,
                "average_response_time": sum(c.average_response_time for c in chatbots) / len(chatbots) if chatbots else 0
            },
            "vectorstores": vectorstores
        }
    except Exception as e:
        logger.error(f"Error getting overview analytics: {e}")
        return {"success": False, "error": str(e)}

# Widget endpoint for the embeddable JavaScript
@app.get("/widget.js")
async def get_widget_js():
    """Serve the embeddable widget JavaScript"""
    try:
        with open("static/widget.js", "r") as f:
            content = f.read()
        return JSONResponse(content=content, media_type="application/javascript")
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Widget JavaScript not found")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
