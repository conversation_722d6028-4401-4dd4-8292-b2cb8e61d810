from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class ChatbotStatus(str, Enum):
    """Chatbot deployment status"""
    CREATING = "creating"
    TRAINING = "training"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

class ChatbotPersonality(str, Enum):
    """Chatbot personality types"""
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"
    CASUAL = "casual"
    TECHNICAL = "technical"
    SALES = "sales"

class ChatbotConfig(BaseModel):
    """Configuration for a website chatbot"""
    
    # Basic Settings
    name: str = Field(..., description="Chatbot name")
    description: Optional[str] = Field(None, description="Chatbot description")
    
    # Appearance
    primary_color: str = Field("#667eea", description="Primary color for chatbot UI")
    secondary_color: str = Field("#764ba2", description="Secondary color for chatbot UI")
    logo_url: Optional[str] = Field(None, description="Company logo URL")
    
    # Personality & Behavior
    personality: ChatbotPersonality = Field(ChatbotPersonality.PROFESSIONAL, description="Chatbot personality")
    greeting_message: str = Field("Hello! How can I help you today?", description="Initial greeting message")
    fallback_message: str = Field("I'm sorry, I don't have information about that. Please try rephrasing your question.", description="Fallback message for unknown queries")
    
    # AI Configuration
    max_context_length: int = Field(8192, description="Maximum context length for LLM")
    temperature: float = Field(0.1, description="AI response creativity (0.0-1.0)")
    max_response_length: int = Field(500, description="Maximum response length")
    
    # Knowledge Base
    custom_knowledge: Optional[str] = Field(None, description="Custom knowledge base text")
    include_website_content: bool = Field(True, description="Whether to include crawled website content")
    include_uploaded_documents: bool = Field(True, description="Whether to include uploaded documents")
    
    # Business Rules
    business_hours: Optional[Dict[str, Any]] = Field(None, description="Business hours configuration")
    lead_qualification_enabled: bool = Field(True, description="Enable automatic lead qualification")
    human_handoff_enabled: bool = Field(True, description="Enable human handoff")
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Acme Corp Assistant",
                "description": "AI assistant for Acme Corporation",
                "primary_color": "#007bff",
                "personality": "professional",
                "greeting_message": "Welcome to Acme Corp! How can I assist you today?",
                "lead_qualification_enabled": True
            }
        }

class WebsiteChatbot(BaseModel):
    """Website chatbot model"""
    
    # Core Information
    id: str = Field(..., description="Unique chatbot identifier")
    website_url: str = Field(..., description="Website URL where chatbot is deployed")
    company_name: str = Field(..., description="Company name")
    
    # Configuration
    config: ChatbotConfig = Field(..., description="Chatbot configuration")
    
    # Status & Metadata
    status: ChatbotStatus = Field(ChatbotStatus.CREATING, description="Current chatbot status")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    # Technical Details
    vectorstore_path: Optional[str] = Field(None, description="Path to chatbot-specific vector store")
    knowledge_base_size: int = Field(0, description="Number of documents in knowledge base")
    last_training: Optional[datetime] = Field(None, description="Last training timestamp")
    
    # Analytics
    total_conversations: int = Field(0, description="Total conversations handled")
    total_messages: int = Field(0, description="Total messages processed")
    average_response_time: float = Field(0.0, description="Average response time in seconds")
    
    # Embed Code
    embed_code: Optional[str] = Field(None, description="HTML/JavaScript code for website embedding")
    
    @validator('website_url')
    def validate_website_url(cls, v):
        """Validate website URL format"""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Website URL must start with http:// or https://')
        return v
    
    @validator('id')
    def validate_id(cls, v):
        """Validate chatbot ID format"""
        if not v or len(v) < 8:
            raise ValueError('Chatbot ID must be at least 8 characters long')
        return v
    
    def update_status(self, new_status: ChatbotStatus):
        """Update chatbot status and timestamp"""
        self.status = new_status
        self.updated_at = datetime.utcnow()
    
    def increment_metrics(self, conversations: int = 0, messages: int = 0):
        """Increment analytics metrics"""
        self.total_conversations += conversations
        self.total_messages += messages
        self.updated_at = datetime.utcnow()
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "chatbot_12345",
                "website_url": "https://example.com",
                "company_name": "Example Corp",
                "config": {
                    "name": "Example Assistant",
                    "personality": "professional"
                },
                "status": "active",
                "total_conversations": 150,
                "total_messages": 450
            }
        }

class CreateChatbotRequest(BaseModel):
    """Request model for creating a new chatbot"""
    website_url: str = Field(..., description="Website URL")
    company_name: str = Field(..., description="Company name")
    config: ChatbotConfig = Field(..., description="Chatbot configuration")

class UpdateChatbotRequest(BaseModel):
    """Request model for updating a chatbot"""
    config: Optional[ChatbotConfig] = Field(None, description="Updated configuration")
    status: Optional[ChatbotStatus] = Field(None, description="New status")

class ChatbotResponse(BaseModel):
    """Response model for chatbot operations"""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if operation failed") 