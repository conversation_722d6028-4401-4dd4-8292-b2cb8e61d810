from pydantic import Base<PERSON>odel, <PERSON>, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class MessageRole(str, Enum):
    """Message role types"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class MessageType(str, Enum):
    """Message content types"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    QUICK_REPLY = "quick_reply"

class IntentType(str, Enum):
    """Detected intent types"""
    GENERAL_QUESTION = "general_question"
    PRODUCT_INQUIRY = "product_inquiry"
    SUPPORT_REQUEST = "support_request"
    LEAD_QUALIFICATION = "lead_qualification"
    APPOINTMENT_BOOKING = "appointment_booking"
    PRICING_INQUIRY = "pricing_inquiry"
    UNKNOWN = "unknown"

class Message(BaseModel):
    """Individual message in a conversation"""
    
    id: str = Field(..., description="Unique message identifier")
    conversation_id: str = Field(..., description="Parent conversation ID")
    
    # Content
    content: str = Field(..., description="Message content")
    role: MessageRole = Field(..., description="Message role (user/assistant/system)")
    message_type: MessageType = Field(MessageType.TEXT, description="Message type")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    processing_time: Optional[float] = Field(None, description="Time taken to process message (seconds)")
    
    # AI Analysis
    detected_intent: Optional[IntentType] = Field(None, description="Detected user intent")
    confidence_score: Optional[float] = Field(None, description="Intent detection confidence (0.0-1.0)")
    entities: Optional[List[Dict[str, Any]]] = Field(None, description="Extracted entities")
    
    # User Context
    user_ip: Optional[str] = Field(None, description="User IP address")
    user_agent: Optional[str] = Field(None, description="User browser/device info")
    session_id: Optional[str] = Field(None, description="User session identifier")
    
    # Business Logic
    is_lead_qualified: Optional[bool] = Field(None, description="Whether message qualifies as a lead")
    lead_score: Optional[float] = Field(None, description="Lead qualification score (0.0-1.0)")
    requires_human_handoff: bool = Field(False, description="Whether message requires human intervention")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "msg_12345",
                "conversation_id": "conv_67890",
                "content": "What are your pricing plans?",
                "role": "user",
                "detected_intent": "pricing_inquiry",
                "confidence_score": 0.95,
                "is_lead_qualified": True,
                "lead_score": 0.8
            }
        }

class Conversation(BaseModel):
    """Complete conversation between user and chatbot"""
    
    id: str = Field(..., description="Unique conversation identifier")
    chatbot_id: str = Field(..., description="Associated chatbot ID")
    
    # Participants
    user_id: Optional[str] = Field(None, description="User identifier (if authenticated)")
    session_id: str = Field(..., description="User session identifier")
    
    # Metadata
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Conversation start time")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="Last activity timestamp")
    ended_at: Optional[datetime] = Field(None, description="Conversation end time")
    
    # Status
    is_active: bool = Field(True, description="Whether conversation is currently active")
    total_messages: int = Field(0, description="Total messages in conversation")
    
    # Analytics
    user_satisfaction: Optional[float] = Field(None, description="User satisfaction rating (1.0-5.0)")
    resolution_time: Optional[float] = Field(None, description="Time to resolution (seconds)")
    
    # Business Metrics
    lead_qualified: bool = Field(False, description="Whether conversation resulted in lead qualification")
    lead_score: float = Field(0.0, description="Overall lead qualification score")
    human_handoff_occurred: bool = Field(False, description="Whether human handoff occurred")
    
    # Messages (not stored in DB, loaded on demand)
    messages: Optional[List[Message]] = Field(None, description="Conversation messages")
    
    @validator('id')
    def validate_id(cls, v):
        """Validate conversation ID format"""
        if not v or len(v) < 8:
            raise ValueError('Conversation ID must be at least 8 characters long')
        return v
    
    def add_message(self, message: Message):
        """Add a message to the conversation"""
        if self.messages is None:
            self.messages = []
        self.messages.append(message)
        self.total_messages = len(self.messages)
        self.last_activity = datetime.utcnow()
    
    def end_conversation(self):
        """Mark conversation as ended"""
        self.is_active = False
        self.ended_at = datetime.utcnow()
    
    def calculate_lead_score(self) -> float:
        """Calculate overall lead score based on messages"""
        if not self.messages:
            return 0.0
        
        lead_scores = [msg.lead_score for msg in self.messages if msg.lead_score is not None]
        if not lead_scores:
            return 0.0
        
        # Weight recent messages more heavily
        weighted_scores = []
        for i, score in enumerate(lead_scores):
            weight = 1.0 + (i * 0.1)  # Recent messages get higher weight
            weighted_scores.append(score * weight)
        
        return sum(weighted_scores) / len(weighted_scores)
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "conv_67890",
                "chatbot_id": "chatbot_12345",
                "session_id": "sess_abc123",
                "total_messages": 5,
                "lead_qualified": True,
                "lead_score": 0.75
            }
        }

class ConversationSummary(BaseModel):
    """Summary of conversation for analytics"""
    
    conversation_id: str = Field(..., description="Conversation identifier")
    chatbot_id: str = Field(..., description="Chatbot identifier")
    
    # Duration
    duration_seconds: float = Field(..., description="Total conversation duration")
    
    # Message Counts
    user_messages: int = Field(..., description="Number of user messages")
    assistant_messages: int = Field(..., description="Number of assistant messages")
    
    # Intent Distribution
    intent_distribution: Dict[str, int] = Field(..., description="Distribution of detected intents")
    
    # Business Impact
    lead_qualified: bool = Field(..., description="Lead qualification status")
    lead_score: float = Field(..., description="Final lead score")
    human_handoff: bool = Field(..., description="Human handoff occurrence")
    
    # User Experience
    average_response_time: float = Field(..., description="Average response time")
    user_satisfaction: Optional[float] = Field(None, description="User satisfaction rating")

class CreateConversationRequest(BaseModel):
    """Request model for creating a new conversation"""
    chatbot_id: str = Field(..., description="Chatbot identifier")
    session_id: str = Field(..., description="User session identifier")
    user_id: Optional[str] = Field(None, description="User identifier (if authenticated)")

class SendMessageRequest(BaseModel):
    """Request model for sending a message"""
    conversation_id: str = Field(..., description="Conversation identifier")
    content: str = Field(..., description="Message content")
    session_id: str = Field(..., description="User session identifier") 